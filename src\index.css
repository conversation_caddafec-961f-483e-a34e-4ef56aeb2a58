@import "theme";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  input[type="checkbox"] {
    accent-color: var(--light-primary-background-rest);
    outline: none;
  }

  /* input:focus, */
  textarea:focus {
    outline: none;
    border: none;
    box-shadow: none;
  }

  input:focus {
    outline: none;
    border: 1px solid var(--light-primary-background-rest);
  }

  svg {
    font-family: var(--body-medium-font-family), serif !important;
  }

  .responsive-svg svg {
    width: 100% !important;
  }

  .datepicker-container {
    width: 100%;
  }

  .datepicker-container .rmdp-input {
    border: none;
    background-color: transparent;
    width: 100%;
  }
  .datepicker-container .rmdp-input:focus {
    box-shadow: none;
    border: none;
  }

  /* Bold Large */
  .font-body-bold-large {
    font-family: var(--body-bold-large-font-family), serif;
    font-size: var(--body-bold-large-font-size);
    font-style: var(--body-bold-large-font-style);
    font-weight: var(--body-bold-large-font-weight);
    letter-spacing: var(--body-bold-large-letter-spacing);
    line-height: var(--body-bold-large-line-height);
  }

  /* Bold Medium */
  .font-body-bold-medium {
    font-family: var(--body-bold-medium-font-family), serif;
    font-size: var(--body-bold-medium-font-size);
    font-style: var(--body-bold-medium-font-style);
    font-weight: var(--body-bold-medium-font-weight);
    letter-spacing: var(--body-bold-medium-letter-spacing);
    line-height: var(--body-bold-medium-line-height);
  }

  /* Bold Small */
  .font-body-bold-small {
    font-family: var(--body-bold-small-font-family), serif;
    font-size: var(--body-bold-small-font-size);
    font-style: var(--body-bold-small-font-style);
    font-weight: var(--body-bold-small-font-weight);
    letter-spacing: var(--body-bold-small-letter-spacing);
    line-height: var(--body-bold-small-line-height);
  }

  /* Large */
  .font-body-large {
    font-family: var(--body-large-font-family), serif;
    font-size: var(--body-large-font-size);
    font-style: var(--body-large-font-style);
    font-weight: var(--body-large-font-weight);
    letter-spacing: var(--body-large-letter-spacing);
    line-height: var(--body-large-line-height);
  }

  /* Medium */
  .font-body-medium {
    font-family: var(--body-medium-font-family), serif;
    font-size: var(--body-medium-font-size);
    font-style: var(--body-medium-font-style);
    font-weight: var(--body-medium-font-weight);
    letter-spacing: var(--body-medium-letter-spacing);
    line-height: var(--body-medium-line-height);
  }

  /* Small */
  .font-body-small {
    font-family: var(--body-small-font-family), serif;
    font-size: var(--body-small-font-size);
    font-style: var(--body-small-font-style);
    font-weight: var(--body-small-font-weight);
    letter-spacing: var(--body-small-letter-spacing);
    line-height: var(--body-small-line-height);
  }

  /* Button Large */
  .font-button-large {
    font-family: var(--button-large-font-family), serif;
    font-size: var(--button-large-font-size);
    font-style: var(--button-large-font-style);
    font-weight: var(--button-large-font-weight);
    letter-spacing: var(--button-large-letter-spacing);
    line-height: var(--button-large-line-height);
  }

  /* Button Medium */
  .font-button-medium {
    font-family: var(--button-medium-font-family), serif;
    font-size: var(--button-medium-font-size);
    font-style: var(--button-medium-font-style);
    font-weight: var(--button-medium-font-weight);
    letter-spacing: var(--button-medium-letter-spacing);
    line-height: var(--button-medium-line-height);
  }

  /* Button Small */
  .font-button-small {
    font-family: var(--button-small-font-family), serif;
    font-size: var(--button-small-font-size);
    font-style: var(--button-small-font-style);
    font-weight: var(--button-small-font-weight);
    letter-spacing: var(--button-small-letter-spacing);
    line-height: var(--button-small-line-height);
  }

  /* Headline Large */
  .font-headline-large {
    font-family: var(--headline-large-font-family), serif;
    font-size: var(--headline-large-font-size);
    font-style: var(--headline-large-font-style);
    font-weight: var(--headline-large-font-weight);
    letter-spacing: var(--headline-large-letter-spacing);
    line-height: var(--headline-large-line-height);
  }

  /* Headline Medium */
  .font-headline-medium {
    font-family: var(--headline-medium-font-family), serif;
    font-size: var(--headline-medium-font-size);
    font-style: var(--headline-medium-font-style);
    font-weight: var(--headline-medium-font-weight);
    letter-spacing: var(--headline-medium-letter-spacing);
    line-height: var(--headline-medium-line-height);
  }

  /* Headline Small */
  .font-headline-small {
    font-family: var(--headline-small-font-family), serif;
    font-size: var(--headline-small-font-size);
    font-style: var(--headline-small-font-style);
    font-weight: var(--headline-small-font-weight);
    letter-spacing: var(--headline-small-letter-spacing);
    line-height: var(--headline-small-line-height);
  }

  /* Overline Large */
  .font-overline-large {
    font-family: var(--overline-large-font-family), serif;
    font-size: var(--overline-large-font-size);
    font-style: var(--overline-large-font-style);
    font-weight: var(--overline-large-font-weight);
    letter-spacing: var(--overline-large-letter-spacing);
    line-height: var(--overline-large-line-height);
  }

  /* Overline Medium */
  .font-overline-medium {
    font-family: var(--overline-medium-font-family), serif;
    font-size: var(--overline-medium-font-size);
    font-style: var(--overline-medium-font-style);
    font-weight: var(--overline-medium-font-weight);
    letter-spacing: var(--overline-medium-letter-spacing);
    line-height: var(--overline-medium-line-height);
  }

  /* Overline Small */
  .font-overline-small {
    font-family: var(--overline-small-font-family), serif;
    font-size: var(--overline-small-font-size);
    font-style: var(--overline-small-font-style);
    font-weight: var(--overline-small-font-weight);
    letter-spacing: var(--overline-small-letter-spacing);
    line-height: var(--overline-small-line-height);
  }

  /* Paragraph Bold Large */
  .font-paragraph-bold-large {
    font-family: var(--paragraph-bold-large-font-family), serif;
    font-size: var(--paragraph-bold-large-font-size);
    font-style: var(--paragraph-bold-large-font-style);
    font-weight: var(--paragraph-bold-large-font-weight);
    letter-spacing: var(--paragraph-bold-large-letter-spacing);
    line-height: var(--paragraph-bold-large-line-height);
  }

  /* Paragraph Bold Medium */
  .font-paragraph-bold-medium {
    font-family: var(--paragraph-bold-medium-font-family), serif;
    font-size: var(--paragraph-bold-medium-font-size);
    font-style: var(--paragraph-bold-medium-font-style);
    font-weight: var(--paragraph-bold-medium-font-weight);
    letter-spacing: var(--paragraph-bold-medium-letter-spacing);
    line-height: var(--paragraph-bold-medium-line-height);
  }

  /* Paragraph Bold Small */
  .font-paragraph-bold-small {
    font-family: var(--paragraph-bold-small-font-family), serif;
    font-size: var(--paragraph-bold-small-font-size);
    font-style: var(--paragraph-bold-small-font-style);
    font-weight: var(--paragraph-bold-small-font-weight);
    letter-spacing: var(--paragraph-bold-small-letter-spacing);
    line-height: var(--paragraph-bold-small-line-height);
  }

  /* Paragraph Large */
  .font-paragraph-large {
    font-family: var(--paragraph-large-font-family), serif;
    font-size: var(--paragraph-large-font-size);
    font-style: var(--paragraph-large-font-style);
    font-weight: var(--paragraph-large-font-weight);
    letter-spacing: var(--paragraph-large-letter-spacing);
    line-height: var(--paragraph-large-line-height);
  }

  /* Paragraph Medium */
  .font-paragraph-medium {
    font-family: var(--paragraph-medium-font-family), serif;
    font-size: var(--paragraph-medium-font-size);
    font-style: var(--paragraph-medium-font-style);
    font-weight: var(--paragraph-medium-font-weight);
    letter-spacing: var(--paragraph-medium-letter-spacing);
    line-height: var(--paragraph-medium-line-height);
  }

  /* Paragraph Small */
  .font-paragraph-small {
    font-family: var(--paragraph-small-font-family), serif;
    font-size: var(--paragraph-small-font-size);
    font-style: var(--paragraph-small-font-style);
    font-weight: var(--paragraph-small-font-weight);
    letter-spacing: var(--paragraph-small-letter-spacing);
    line-height: var(--paragraph-small-line-height);
  }

  /* Subtitle Large */
  .font-subtitle-large {
    font-family: var(--subtitle-large-font-family), serif;
    font-size: var(--subtitle-large-font-size);
    font-style: var(--subtitle-large-font-style);
    font-weight: var(--subtitle-large-font-weight);
    letter-spacing: var(--subtitle-large-letter-spacing);
    line-height: var(--subtitle-large-line-height);
  }

  /* Subtitle Medium */
  .font-subtitle-medium {
    font-family: var(--subtitle-medium-font-family), serif;
    font-size: var(--subtitle-medium-font-size);
    font-style: var(--subtitle-medium-font-style);
    font-weight: var(--subtitle-medium-font-weight);
    letter-spacing: var(--subtitle-medium-letter-spacing);
    line-height: var(--subtitle-medium-line-height);
  }

  /* Subtitle Small */
  .font-subtitle-small {
    font-family: var(--subtitle-small-font-family), serif;
    font-size: var(--subtitle-small-font-size);
    font-style: var(--subtitle-small-font-style);
    font-weight: var(--subtitle-small-font-weight);
    letter-spacing: var(--subtitle-small-letter-spacing);
    line-height: var(--subtitle-small-line-height);
  }

  /* Title Large */
  .font-title-large {
    font-family: var(--title-large-font-family), serif;
    font-size: var(--title-large-font-size);
    font-style: var(--title-large-font-style);
    font-weight: var(--title-large-font-weight);
    letter-spacing: var(--title-large-letter-spacing);
    line-height: var(--title-large-line-height);
  }

  /* Title Medium */
  .font-title-medium {
    font-family: var(--title-medium-font-family), serif;
    font-size: var(--title-medium-font-size);
    font-style: var(--title-medium-font-style);
    font-weight: var(--title-medium-font-weight);
    letter-spacing: var(--title-medium-letter-spacing);
    line-height: var(--title-medium-line-height);
  }

  /* Title Small */
  .font-title-small {
    font-family: var(--title-small-font-family), serif;
    font-size: var(--title-small-font-size);
    font-style: var(--title-small-font-style);
    font-weight: var(--title-small-font-weight);
    letter-spacing: var(--title-small-letter-spacing);
    line-height: var(--title-small-line-height);
  }
}

/* Custom button styles */
.c-button {
  @apply inline-flex items-center justify-center w-full rounded-lg focus:outline-none
  text-light-neutral-text-white bg-light-primary-background-rest hover:bg-light-primary-background-hover
  px-4 py-2 h-[40px] font-button-medium;

  /* Define sizes */
  &-sm {
    @apply px-2.5 py-1.5 h-[32px] font-button-small;
  }
  &-md {
    @apply px-4 py-2 h-[40px] font-button-medium;
  }
  &-lg {
    @apply px-6 py-3 h-[48px] font-button-large;
  }

  &-primary {
    &-fill {
      @apply text-black bg-light-primary-background-rest
      hover:bg-light-primary-background-hover
      disabled:text-light-neutral-text-disable disabled:bg-light-neutral-background-disable
      disabled:pointer-events-none;
    }

    &-outline {
      @apply text-light-primary-text-rest bg-transparent border border-light-primary-border-rest
      hover:bg-light-primary-background-highlight hover:text-light-primary-text-hover
      hover:border-light-primary-border-hover
      disabled:text-light-neutral-text-disable disabled:border-light-neutral-border-disable
      disabled:pointer-events-none;
    }

    &-text {
      @apply text-light-primary-text-rest bg-transparent border-none
      hover:bg-light-primary-background-highlight hover:text-light-primary-text-hover
      disabled:text-light-neutral-text-disable disabled:pointer-events-none;
    }
  }
  &-neutral {
    &-fill {
      @apply text-light-neutral-text-high bg-light-neutral-background-medium
      hover:bg-light-neutral-background-low
      disabled:text-light-neutral-text-disable disabled:bg-light-neutral-background-disable
      disabled:pointer-events-none;
    }

    &-outline {
      @apply text-light-primary-text-rest bg-transparent border border-light-neutral-border-medium-rest
      hover:bg-light-neutral-background-low hover:text-light-neutral-text-high
      hover:border-light-neutral-border-medium-hover
      disabled:text-light-neutral-text-disable disabled:border-light-neutral-border-disable
      disabled:pointer-events-none;
    }

    &-text {
      @apply text-light-neutral-text-high bg-transparent border-none
      hover:bg-light-neutral-background-low hover:text-light-neutral-text-high
      disabled:text-light-neutral-text-disable disabled:pointer-events-none;
    }
  }
  &-success {
    &-fill {
      @apply text-light-neutral-text-white bg-light-success-background-rest
      hover:bg-light-success-background-hover
      disabled:text-light-neutral-text-disable disabled:bg-light-neutral-background-disable
      disabled:pointer-events-none;
    }

    &-outline {
      @apply text-light-success-text-rest bg-transparent border border-light-success-border-rest
      hover:bg-light-success-background-highlight hover:text-light-success-text-hover
      hover:border-light-success-border-hover
      disabled:text-light-neutral-text-disable disabled:border-light-neutral-border-disable
      disabled:pointer-events-none;
    }

    &-text {
      @apply text-light-success-text-rest bg-transparent border-none
      hover:bg-light-success-background-highlight hover:text-light-success-text-rest
      disabled:text-light-neutral-text-disable disabled:pointer-events-none;
    }
  }
  &-error {
    &-fill {
      @apply text-light-neutral-text-white bg-light-error-background-rest
      hover:bg-light-error-background-hover
      disabled:text-light-neutral-text-disable disabled:bg-light-neutral-background-disable
      disabled:pointer-events-none;
    }

    &-outline {
      @apply text-light-error-text-rest bg-transparent border border-light-error-border-rest
      hover:bg-light-error-background-highlight hover:text-light-error-text-hover
      hover:border-light-error-border-hover
      disabled:text-light-neutral-text-disable disabled:border-light-neutral-border-disable
      disabled:pointer-events-none;
    }

    &-text {
      @apply text-light-error-text-rest bg-transparent border-none
      hover:bg-light-error-background-highlight hover:text-light-error-text-rest
      disabled:text-light-neutral-text-disable disabled:pointer-events-none;
    }
  }
}

/* Custom input styles */
.c-input {
  @apply mb-4 relative;

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: var(--light-neutral-text-high);
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px transparent;
  }

  & > .label-wrapper {
    @apply flex flex-row justify-between font-overline-large;
  }
  & > .label-wrapper > label {
    @apply flex-1 text-light-neutral-text-high text-right mb-1;
  }
  & > .label-wrapper > a {
    @apply flex text-light-primary-text-rest font-overline-large mb-1;
  }

  & > .input-wrapper {
    @apply flex flex-row justify-between items-center gap-2 mb-1 border border-light-neutral-border-medium-rest rounded-md py-0 m-0;

    & > input {
      @apply flex-1 bg-transparent border-none appearance-none border rounded size-full py-2 px-3 text-light-neutral-text-high leading-tight
      focus:outline-none;
    }
    & > .action-icon {
      @apply text-[20px] text-light-neutral-text-low;
    }
    & > .inner-label {
      @apply text-light-neutral-text-medium font-overline-large;
    }
    & > .text-action {
      @apply text-light-primary-text-rest font-overline-large;
    }
  }

  & > .input-dropdown {
    ::-webkit-scrollbar {
      width: 2px;
    }
    ::-webkit-scrollbar-track {
      border-radius: 8px;
      background: var(--light-neutral-background-medium);
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 8px;
      background: var(--light-neutral-text-low);
    }
    ::-webkit-scrollbar-thumb:hover {
      background: var(--light-neutral-text-low);
    }

    @apply flex flex-col w-full px-4 pt-4 pb-2 rounded-[8px] absolute z-50 bg-light-neutral-surface-card shadow-[0_2px_20px_0_rgba(0,0,0,0.05)];

    & > .dropdown-wrapper {
      @apply flex flex-col gap-1 w-full pl-1 max-h-[190px] overflow-y-scroll scrollbar-thin [direction:rtl];

      & > .dropdown-item {
        @apply flex flex-row justify-between p-2 rounded-[8px] bg-light-neutral-surface-card;

        &:hover {
          @apply bg-light-neutral-surface-highlight;
        }
      }
    }
  }

  & > .hint-wrapper {
    @apply flex flex-col;
  }
  & > .hint-wrapper > p {
    @apply flex-1 font-overline-medium text-light-neutral-text-medium text-right mb-1;
  }
  & > .hint-wrapper > .success-message {
    @apply text-light-success-text-rest;
  }
  & > .hint-wrapper > .error-message {
    @apply text-light-error-text-rest;
  }

  /* Define sizes */
  &-sm {
    & > .input-wrapper {
      @apply px-4 h-[32px] font-body-medium;
    }
    & > .input-wrapper > .inner-label,
    & > .input-wrapper > .text-action {
      @apply font-overline-medium;
    }
    & > .label-wrapper > label {
      @apply font-overline-medium;
    }
  }
  &-md {
    & > .input-wrapper {
      @apply px-4 h-[40px] font-body-large;
    }
  }
  &-lg {
    & > .input-wrapper {
      @apply px-4 h-[48px] font-body-large;
    }
  }

  &-rtl {
    & > .input-wrapper {
      direction: rtl;
    }
  }
  &-ltr {
    & > .input-wrapper {
      direction: ltr;
    }
  }

  &-primary {
  }
  &-neutral {
  }
  &-success {
    & > .input-wrapper {
      @apply border-light-success-border-rest;
    }
  }
  &-error {
    & > .input-wrapper {
      @apply border-light-error-border-rest;
    }
  }

  &-inset {
    & > .input-wrapper {
      @apply border-light-neutral-border-low-rest bg-light-neutral-background-low;
    }
  }
}

/* Custom OTP input styles */
.c-otp-input {
  @apply mb-4;

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-background-clip: text;
    -webkit-text-fill-color: var(--light-neutral-text-high);
    transition: background-color 5000s ease-in-out 0s;
    box-shadow: inset 0 0 20px 20px #fff;
  }

  & > .label-wrapper {
    @apply flex flex-row justify-between;
  }
  & > .label-wrapper > label {
    @apply flex-1 text-light-neutral-text-high text-right mb-1;
  }
  & > .label-wrapper > a {
    @apply flex text-light-primary-text-rest font-overline-large mb-1;
  }

  & > .input-wrapper {
    @apply flex flex-row justify-between items-center border-none p-0 m-0 mb-1;

    .input-style {
      @apply size-full rounded-lg border border-light-neutral-border-medium-rest font-body-large
      focus:outline-none focus:border-light-neutral-border-high-rest;
    }
  }

  & > .hint-wrapper {
    @apply flex flex-col;
  }
  & > .hint-wrapper > p {
    @apply flex-1 font-overline-medium text-light-neutral-text-medium text-right mb-1;
  }
  & > .hint-wrapper > .caption {
    @apply text-left;
  }
  & > .hint-wrapper > .success-message {
    @apply text-light-success-text-rest;
  }
  & > .hint-wrapper > .error-message {
    @apply text-light-error-text-rest;
  }

  /* Define sizes */
  &-sm {
    & > .input-wrapper {
      @apply h-[32px] font-body-medium;
    }
    & .input-style {
      @apply w-[32px] font-body-medium;
    }
    & > .label-wrapper > label {
      @apply font-overline-medium;
    }
  }
  &-md {
    & > .input-wrapper {
      @apply h-[40px] font-body-large;
    }
    & .input-style {
      @apply !w-[40px] font-body-large;
    }
  }
  &-lg {
    & > .input-wrapper {
      @apply h-[48px] font-body-large;
    }
    & .input-style {
      @apply !w-[48px] font-body-large;
    }
  }

  &-rtl {
  }
  &-ltr {
    & > .input-wrapper {
      direction: ltr;
    }
  }

  &-primary {
  }
  &-neutral {
  }
  &-success {
    & > .input-wrapper input {
      @apply border-light-success-border-rest;
    }
  }
  &-error {
    & > .input-wrapper input {
      @apply border-light-error-border-rest;
    }
  }
}
/* react-multi-date-picker */
.rmdp-day.rmdp-selected span:not(.highlight) {
  background-color: #432fa7 !important;
  border-radius: 4px !important;
}
.rmdp-range {
  background-color: #432fa7 !important;
  /* border-radius: 4px !important; */
}
.rmdp-day:not(.rmdp-disabled, .rmdp-day-hidden) span:hover {
  background-color: #b4abe34d !important;
  color: #000 !important;
  border-radius: 4px !important;
}
.rmdp-week-day {
  color: #000 !important;
  opacity: 0.5 !important;
}
.rmdp-day.rmdp-today span {
  background-color: #432fa768 !important;
  border-radius: 4px !important;
}
.rmdp-week,
.rmdp-header-values,
.rmdp-input {
  font-family: iranyekan;
}
.ck .ck-content {
  padding: 0 26px !important;
}
/* ---- */
.ck-editor__editable_inline {
  min-height: 200px !important;
}
.ck .ck-balloon-panel {
  display: none !important;
}
.editor-content {
  border: 1px solid #ccc;
  border-radius: 4px;
  padding: 8px;
  min-height: 150px;
  font-size: 14px;
  line-height: 1.5;
  outline: none;
}

.editor-placeholder {
  color: #aaa;
  position: absolute;
  pointer-events: none;
}
body {
  margin: 0;
  background: #eee;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, ".SFNSText-Regular",
    sans-serif;
  font-weight: 500;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.actions {
  position: absolute;
  text-align: right;
  padding: 10px;
  bottom: 0;
  right: 0;
}

.actions i {
  background-size: contain;
  display: inline-block;
  height: 15px;
  width: 15px;
  vertical-align: -0.25em;
}

.action-button {
  background-color: #eee;
  border: 0;
  padding: 8px 12px;
  position: relative;
  margin-left: 5px;
  border-radius: 15px;
  color: #222;
  display: inline-block;
  cursor: pointer;
}

.action-button:hover {
  background-color: #ddd;
  color: #000;
}

button.action-button:disabled {
  opacity: 0.6;
  background: #eee;
  cursor: not-allowed;
}

.other h2 {
  font-size: 18px;
  color: #444;
  margin-bottom: 7px;
}

.other a {
  color: #777;
  text-decoration: underline;
  font-size: 14px;
}

.other ul {
  padding: 0;
  margin: 0;
  list-style-type: none;
}

.App {
  font-family: sans-serif;
  text-align: center;
  padding: 10px 5%;
}

h1 {
  font-size: 24px;
  color: #333;
}

.ltr {
  text-align: left;
}

.rtl {
  text-align: right;
}

.editor-container {
  border: solid 1px;
  /* margin: 20px auto 20px auto; */
  border-radius: 2px;
  /* max-width: 600px; */
  color: #000;
  position: relative;
  line-height: 20px;
  font-weight: 400;
  text-align: left;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.editor-inner {
  background: #fff;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  position: relative;
}

.editor-input {
  min-height: 150px;
  resize: none;
  font-size: 15px;
  caret-color: rgb(5, 5, 5);
  position: relative;
  tab-size: 1;
  outline: 0;
  padding: 10px;
  caret-color: #444;
}

.editor-placeholder {
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: -10px;
  right: 10px;
  font-size: 15px;
  user-select: none;
  display: inline-block;
  pointer-events: none;
}

.editor-text-bold {
  font-weight: bold;
}

.editor-text-italic {
  font-style: italic;
}

.editor-text-underline {
  text-decoration: underline;
}

.editor-text-strikethrough {
  text-decoration: line-through;
}

.editor-text-underlineStrikethrough {
  text-decoration: underline line-through;
}

.editor-text-code {
  background-color: rgb(240, 242, 245);
  padding: 1px 0.25rem;
  font-family: Menlo, Consolas, Monaco, monospace;
  font-size: 94%;
}

.editor-link {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor-code {
  background-color: rgb(240, 242, 245);
  font-family: Menlo, Consolas, Monaco, monospace;
  display: block;
  padding: 8px 8px 8px 52px;
  line-height: 1.53;
  font-size: 13px;
  margin: 0;
  margin-top: 8px;
  margin-bottom: 8px;
  tab-size: 2;
  white-space: pre;
  overflow-x: auto;
  position: relative;
}

.editor-code:before {
  content: attr(data-gutter);
  position: absolute;
  background-color: #eee;
  left: 0;
  top: 0;
  border-right: 1px solid #ccc;
  padding: 8px;
  color: #777;
  white-space: pre-wrap;
  text-align: right;
  min-width: 25px;
}
.editor-code:after {
  content: attr(data-highlight-language);
  top: 2%;
  right: 5px;
  padding: 3px;
  font-size: 10px;
  text-transform: uppercase;
  position: absolute;
  color: rgba(0, 0, 0, 0.5);
}

.editor-tokenComment {
  color: slategray;
}

.editor-tokenPunctuation {
  color: #999;
}

.editor-tokenProperty {
  color: #905;
}

.editor-tokenSelector {
  color: #690;
}

.editor-tokenOperator {
  color: #9a6e3a;
}

.editor-tokenAttr {
  color: #07a;
}

.editor-tokenVariable {
  color: #e90;
}

.editor-tokenFunction {
  color: #dd4a68;
}

.editor-paragraph {
  margin: 0;
  margin-bottom: 8px;
  position: relative;
}

.editor-paragraph:last-child {
  margin-bottom: 0;
}

.editor-heading-h1 {
  font-size: 24px;
  color: rgb(5, 5, 5);
  font-weight: 400;
  margin: 0;
  margin-bottom: 12px;
  padding: 0;
}

.editor-heading-h2 {
  font-size: 15px;
  color: rgb(101, 103, 107);
  font-weight: 700;
  margin: 0;
  margin-top: 10px;
  padding: 0;
  text-transform: uppercase;
}

.editor-quote {
  margin: 0;
  margin-left: 20px;
  font-size: 15px;
  color: rgb(101, 103, 107);
  border-left-color: rgb(206, 208, 212);
  border-left-width: 4px;
  border-left-style: solid;
  padding-left: 16px;
}

.editor-list-ol {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-list-ul {
  padding: 0;
  margin: 0;
  margin-left: 16px;
}

.editor-listitem {
  margin: 8px 32px 8px 32px;
}

.editor-nested-listitem {
  list-style-type: none;
}

pre::-webkit-scrollbar {
  background: transparent;
  width: 10px;
}

pre::-webkit-scrollbar-thumb {
  background: #999;
}

.toolbar {
  display: flex;
  justify-content: end;
  margin-bottom: 1px;
  background: #fff;
  padding: 4px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  vertical-align: middle;
}

.toolbar button.toolbar-item {
  border: 0;
  display: flex;
  background: none;
  border-radius: 10px;
  padding: 8px;
  cursor: pointer;
  vertical-align: middle;
}

.toolbar button.toolbar-item:disabled {
  cursor: not-allowed;
}

.toolbar button.toolbar-item.spaced {
  margin-right: 2px;
}

.toolbar button.toolbar-item i.format {
  background-size: contain;
  display: inline-block;
  height: 18px;
  width: 18px;
  margin-top: 2px;
  vertical-align: -0.25em;
  display: flex;
  opacity: 0.6;
}

.toolbar button.toolbar-item:disabled i.format {
  opacity: 0.2;
}

.toolbar button.toolbar-item.active {
  background-color: rgba(223, 232, 250, 0.3);
}

.toolbar button.toolbar-item.active i {
  opacity: 1;
}

.toolbar .toolbar-item:hover:not([disabled]) {
  background-color: #eee;
}

.toolbar .divider {
  width: 1px;
  background-color: #eee;
  margin: 0 4px;
}

.toolbar select.toolbar-item {
  border: 0;
  display: flex;
  background: none;
  border-radius: 10px;
  padding: 8px;
  vertical-align: middle;
  -webkit-appearance: none;
  -moz-appearance: none;
  width: 70px;
  font-size: 14px;
  color: #777;
  text-overflow: ellipsis;
}

.toolbar select.code-language {
  text-transform: capitalize;
  width: 130px;
}

.toolbar .toolbar-item .text {
  display: flex;
  line-height: 20px;
  width: 200px;
  vertical-align: middle;
  font-size: 14px;
  color: #777;
  text-overflow: ellipsis;
  width: 70px;
  overflow: hidden;
  height: 20px;
  text-align: left;
}

.toolbar .toolbar-item .icon {
  display: flex;
  width: 20px;
  height: 20px;
  user-select: none;
  margin-right: 8px;
  line-height: 16px;
  background-size: contain;
}

.toolbar i.chevron-down {
  margin-top: 3px;
  width: 16px;
  height: 16px;
  display: flex;
  user-select: none;
}

.toolbar i.chevron-down.inside {
  width: 16px;
  height: 16px;
  display: flex;
  margin-left: -25px;
  margin-top: 11px;
  margin-right: 10px;
  pointer-events: none;
}

i.chevron-down {
  background-color: transparent;
  background-size: contain;
  display: inline-block;
  height: 8px;
  width: 8px;
  background-image: url(assets/images/icons/chevron-down.svg);
}

#block-controls button:hover {
  background-color: #efefef;
}

#block-controls button:focus-visible {
  border-color: blue;
}

#block-controls span.block-type {
  background-size: contain;
  display: block;
  width: 18px;
  height: 18px;
  margin: 2px;
}

#block-controls span.block-type.paragraph {
  background-image: url(assets/images/icons/text-paragraph.svg);
}

#block-controls span.block-type.h1 {
  background-image: url(assets/images/icons/type-h1.svg);
}

#block-controls span.block-type.h2 {
  background-image: url(assets/images/icons/type-h2.svg);
}

#block-controls span.block-type.quote {
  background-image: url(assets/images/icons/chat-square-quote.svg);
}

#block-controls span.block-type.ul {
  background-image: url(assets/images/icons/list-ul.svg);
}

#block-controls span.block-type.ol {
  background-image: url(assets/images/icons/list-ol.svg);
}

#block-controls span.block-type.code {
  background-image: url(assets/images/icons/code.svg);
}

.dropdown {
  z-index: 5;
  display: block;
  position: absolute;
  box-shadow: 0 12px 28px 0 rgba(0, 0, 0, 0.2), 0 2px 4px 0 rgba(0, 0, 0, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  min-width: 100px;
  min-height: 40px;
  background-color: #fff;
}

.dropdown .item {
  margin: 0 8px 0 8px;
  padding: 8px;
  color: #050505;
  cursor: pointer;
  line-height: 16px;
  font-size: 15px;
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  justify-content: space-between;
  background-color: #fff;
  border-radius: 8px;
  border: 0;
  min-width: 268px;
}

.dropdown .item .active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

.dropdown .item:first-child {
  margin-top: 8px;
}

.dropdown .item:last-child {
  margin-bottom: 8px;
}

.dropdown .item:hover {
  background-color: #eee;
}

.dropdown .item .text {
  display: flex;
  line-height: 20px;
  flex-grow: 1;
  width: 200px;
}

.dropdown .item .icon {
  display: flex;
  width: 20px;
  height: 20px;
  user-select: none;
  margin-right: 12px;
  line-height: 16px;
  background-size: contain;
}

.link-editor {
  position: absolute;
  z-index: 100;
  top: -10000px;
  left: -10000px;
  margin-top: -6px;
  max-width: 300px;
  width: 100%;
  opacity: 0;
  background-color: #fff;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.3);
  border-radius: 8px;
  transition: opacity 0.5s;
}

.link-editor .link-input {
  display: block;
  width: calc(100% - 24px);
  box-sizing: border-box;
  margin: 8px 12px;
  padding: 8px 12px;
  border-radius: 15px;
  background-color: #eee;
  font-size: 15px;
  color: rgb(5, 5, 5);
  border: 0;
  outline: 0;
  position: relative;
  font-family: inherit;
}

.link-editor div.link-edit {
  background-image: url(assets/images/icons/pencil-fill.svg);
  background-size: 16px;
  background-position: center;
  background-repeat: no-repeat;
  width: 35px;
  vertical-align: -0.25em;
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
}

.link-editor .link-input a {
  color: rgb(33, 111, 219);
  text-decoration: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  margin-right: 30px;
  text-overflow: ellipsis;
}

.link-editor .link-input a:hover {
  text-decoration: underline;
}

.link-editor .button {
  width: 20px;
  height: 20px;
  display: inline-block;
  padding: 6px;
  border-radius: 8px;
  cursor: pointer;
  margin: 0 2px;
}

.link-editor .button.hovered {
  width: 20px;
  height: 20px;
  display: inline-block;
  background-color: #eee;
}

.link-editor .button i,
.actions i {
  background-size: contain;
  display: inline-block;
  height: 20px;
  width: 20px;
  vertical-align: -0.25em;
}

i.undo {
  background-image: url(assets/images/icons/arrow-counterclockwise.svg);
}

i.redo {
  background-image: url(assets/images/icons/arrow-clockwise.svg);
}

.icon.paragraph {
  background-image: url(assets/images/icons/text-paragraph.svg);
}

.icon.large-heading,
.icon.h1 {
  background-image: url(assets/images/icons/type-h1.svg);
}

.icon.small-heading,
.icon.h2 {
  background-image: url(assets/images/icons/type-h2.svg);
}

.icon.bullet-list,
.icon.ul {
  background-image: url(assets/images/icons/list-ul.svg);
}

.icon.numbered-list,
.icon.ol {
  background-image: url(assets/images/icons/list-ol.svg);
}

.icon.quote {
  background-image: url(assets/images/icons/chat-square-quote.svg);
}

.icon.code {
  background-image: url(assets/images/icons/code.svg);
}

i.bold {
  background-image: url(assets/images/icons/type-bold.svg);
}

i.italic {
  background-image: url(assets/images/icons/type-italic.svg);
}

i.underline {
  background-image: url(assets/images/icons/type-underline.svg);
}

i.strikethrough {
  background-image: url(assets/images/icons/type-strikethrough.svg);
}

i.code {
  background-image: url(assets/images/icons/code.svg);
}

i.link {
  background-image: url(assets/images/icons/link.svg);
}

i.left-align {
  background-image: url(assets/images/icons/text-left.svg);
}

i.center-align {
  background-image: url(assets/images/icons/text-center.svg);
}

i.right-align {
  background-image: url(assets/images/icons/text-right.svg);
}

i.justify-align {
  background-image: url(assets/images/icons/justify.svg);
}

i.markdown {
  background-image: url(assets/images/icons/markdown.svg);
}
input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: #6f5cd1; /* Changes the color */
}

/* Remove arrows for all number inputs */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
input[type="number"] {
  -moz-appearance: textfield; /* Remove arrows for Firefox */
}
.range-square {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 2px;
  background: transparent;
  cursor: pointer;
}
/* WebKit (Chrome, Safari) */
.range-square::-webkit-slider-runnable-track {
  height: 2px;
  background: #69696959;
  border-radius: 0;
}
/* Firefox */
.range-square::-moz-range-track {
  height: 2px;
  background: #69696959;
  border-radius: 0;
}
/* Style the thumb */
.range-square::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 14px;
  height: 14px;
  background: #432fa7;
  border-radius: 4px;
  cursor: pointer;
  margin-top: -6px;
}
.range-square::-moz-range-thumb {
  width: 14px;
  height: 14px;
  background: #432fa7;
  border-radius: 4px;
  cursor: pointer;
}
/* ---- */
@media print {
  body {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }
  #content-chart {
    margin-top: 350px !important;
  }
  #content-chart-news {
    margin-top: 50px !important;
  }
  #content-chart-topic {
    margin-top: 90px !important;
  }
  #hashtags-chart {
    margin-top: 200px !important;
  }
  #hashtags-chart-news {
    margin-top: 0px !important;
  }
  #category-chart {
    margin-top: 200px !important;
  }
  #category-chart-news {
    margin-top: 300px !important;
  }
  #release-chart {
    margin-bottom: 50px !important;
  }
  #addTopic {
    display: none !important;
  }
  #addProfile {
    display: none !important;
  }
  #profile-compare-header {
    display: none !important;
  }
  #topic-compare-header {
    display: none !important;
  }
  #repeatWordsTabs {
    display: none !important;
  }
  #repeatHashtagTabs {
    display: none !important;
  }
  .field-actions {
    display: none !important;
  }
  .chart-help-icons {
    display: none !important;
  }
  #topic-repeated-words-chart {
    margin-bottom: 150px !important;
  }
  #topic-gender-chart {
    margin-top: 150px !important;
  }
  #topic-category-chart {
    margin-top: 150px !important;
  }
  #topic-sentiment-chart {
    margin-bottom: 30px !important;
  }
  #profile-fields-box {
    margin-top: 50px;
  }
  #topic-fields-box {
    margin-top: 20px;
  }
}
