import { Route, Routes, Navigate } from "react-router-dom";

// Public Pages
import FormPage from "./pages/formpage";
import NotFound from "./pages/404";
import Error500 from "./pages/500";

import "./App.css";

function App() {
  return (
    <div dir={"rtl"}>
      <Routes>
        <Route path="/" element={<Navigate replace to={"/app"} />} />

        <Route path="/app" element={<FormPage />} />
        <Route path="/500" element={<Error500 />} />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </div>
  );
}

export default App;
