import { Route, Routes, Navigate } from "react-router-dom";

// Public Pages
import Login from "./pages/login";
import NotFound from "./pages/404";
import Error500 from "./pages/500";

import "./App.css";

function App() {
  return (
    <Routes>
      <Route path="/" element={<Navigate replace to={"/app"} />} />

      <Route path="/app" element={<Login />} />
      <Route path="/500" element={<Error500 />} />
      <Route path="*" element={<NotFound />} />
    </Routes>
  );
}

export default App;
