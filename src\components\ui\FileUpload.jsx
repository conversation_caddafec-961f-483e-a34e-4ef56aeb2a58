import { useRef } from "react";
import { PaperclipIcon } from "@phosphor-icons/react";

const FileUpload = ({ onFilesSelected, maxFileSize = 50 * 1024 * 1024 }) => {
  const fileInputRef = useRef(null);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event) => {
    const files = Array.from(event.target.files);
    const validFiles = [];
    const errors = [];

    files.forEach((file) => {
      if (file.size > maxFileSize) {
        errors.push(`${file.name}: حجم فایل بیش از 50MB است`);
      } else {
        validFiles.push(file);
      }
    });

    if (errors.length > 0) {
      alert(errors.join("\n"));
    }

    if (validFiles.length > 0) {
      onFilesSelected(validFiles);
    }

    // Reset input
    event.target.value = "";
  };

  return (
    <>
      <button
        type="button"
        onClick={handleFileSelect}
        className="inline-flex justify-center items-center p-2 text-bla rounded-md cursor-pointer hover:bg-gray-400 bg-light-neutral-background-high"
      >
        <PaperclipIcon size={20} />
        <span className="sr-only">افزودن فایل</span>
      </button>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.zip,.rar"
        onChange={handleFileChange}
        className="hidden"
      />
    </>
  );
};

export default FileUpload;
