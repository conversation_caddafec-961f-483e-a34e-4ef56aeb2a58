import { useEffect, useState } from "react";
import { XIcon } from "@phosphor-icons/react";

const MediaPreviewPopup = ({ isOpen, onClose, file, previewUrl }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setTimeout(() => setIsAnimating(true), 10);
    } else {
      setIsAnimating(false);
      setTimeout(() => setIsVisible(false), 300);
    }
  }, [isOpen]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden';
    } else {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isVisible || !file) return null;

  const isImage = file.type.startsWith('image/');
  const isVideo = file.type.startsWith('video/');

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div
        className={`fixed inset-0 bg-black transition-opacity duration-300 ${
          isAnimating ? "opacity-75" : "opacity-0"
        }`}
        onClick={handleBackdropClick}
      />
      
      {/* Modal Content */}
      <div
        className={`fixed inset-0 flex items-center justify-center p-4 transform transition-all duration-300 ease-out ${
          isAnimating ? "scale-100 opacity-100" : "scale-95 opacity-0"
        }`}
      >
        <div className="relative max-w-4xl max-h-full bg-white rounded-2xl shadow-2xl overflow-hidden">
          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 z-10 w-10 h-10 bg-black bg-opacity-50 hover:bg-opacity-70 text-white rounded-full flex items-center justify-center transition-colors"
          >
            <XIcon size={20} />
          </button>

          {/* Media Content */}
          <div className="relative">
            {isImage && (
              <img
                src={previewUrl}
                alt={file.name}
                className="max-w-full max-h-[80vh] object-contain"
                style={{ minWidth: '300px', minHeight: '200px' }}
              />
            )}
            
            {isVideo && (
              <video
                src={previewUrl}
                controls
                autoPlay
                className="max-w-full max-h-[80vh] object-contain"
                style={{ minWidth: '300px', minHeight: '200px' }}
              >
                مرورگر شما از پخش ویدیو پشتیبانی نمی‌کند.
              </video>
            )}
          </div>

          {/* File Info */}
          <div className="p-4 bg-gray-50 border-t">
            <h3 className="font-medium text-gray-900 truncate">{file.name}</h3>
            <div className="flex items-center justify-between mt-2 text-sm text-gray-600">
              <span>نوع: {file.type}</span>
              <span>حجم: {formatFileSize(file.size)}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 بایت';
  const k = 1024;
  const sizes = ['بایت', 'کیلوبایت', 'مگابایت', 'گیگابایت'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default MediaPreviewPopup;
