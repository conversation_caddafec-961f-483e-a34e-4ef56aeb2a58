import { useState } from "react";
import {
  CaretDownIcon,
  CaretLeftIcon,
  MicrophoneIcon,
  PaperclipIcon,
  TagIcon,
  // ChevronDownIcon,
} from "@phosphor-icons/react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import L from "leaflet";
import loginImg from "../../assets/images/logo.png";
import { CButton } from "../../components/ui/CButton";
import SubjectBottomSheet from "../../components/ui/SubjectBottomSheet";
import { parseTimeToPersian } from "../../utils/helper";

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  iconUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl:
    "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

function FormPage() {
  const [selectedLocation, setSelectedLocation] = useState({
    lat: 35.6892, // Default to Tehran coordinates
    lng: 51.389,
  });
  const [address, setAddress] = useState("");
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [isSubjectBottomSheetOpen, setIsSubjectBottomSheetOpen] =
    useState(false);

  const handleLogin = (values, actions) => {
    // Your login logic here
    // Example: Validate username and password
    if (
      values.username === "your_username" &&
      values.password === "your_password"
    ) {
      // Successful login, redirect or perform actions
      actions.resetForm();
      console.log("Logged in successfully");
    } else {
      actions.setFieldError("username", "نام کاربری یا کلمه عبور اشتباه است");
    }
  };

  const handleMapClick = (e) => {
    const { lat, lng } = e.latlng;
    setSelectedLocation({ lat, lng });

    // Reverse geocoding to get address (you can implement this with a geocoding service)
    setAddress(`موقعیت: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
  };

  return (
    <div
      className={
        "bg-white container m-auto h-screen overflow-hidden font-body-medium max-w-[1280px]"
      }
    >
      <div className="flex w-full justify-center items-center h-16 shadow-md z-30">
        <img src={loginImg} alt="login" className="w-auto h-[15px]" />
      </div>
      <div className={"flex flex-row container m-auto h-full p-4"}>
        <div className="flex flex-col w-full gap-6 overflow-scroll no-scrollbar  pb-36">
          <div className="flex justify-start w-full text-right">
            <span className="font-body-small text-light-neutral-text-medium">
              {parseTimeToPersian(new Date())}
            </span>
          </div>
          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-light-neutral-text-high font-body-medium text-gray-900 "
            >
              موضوع
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <CaretDownIcon size={20} className="text-gray-400" />
              </div>

              <div
                onClick={() => setIsSubjectBottomSheetOpen(true)}
                className="bg-white border border-light-neutral-border-medium-rest text-light-neutral-text-high rounded-xl block w-full p-2.5 cursor-pointer hover:bg-gray-50 transition-colors "
              >
                <span
                  className={
                    selectedSubject ? "text-gray-900" : "text-gray-500"
                  }
                >
                  {selectedSubject ? selectedSubject.title : "انتخاب کنید"}
                </span>
              </div>
            </div>
          </div>
          <div>
            <label
              htmlFor="description"
              className="block mb-2 text-light-neutral-text-high font-body-medium text-gray-900"
            >
              توضیحات گزارش
            </label>
            <div className="w-full mb-4 border border-light-neutral-border-medium-rest rounded-xl bg-white">
              <div className="px-4 py-2 bg-white rounded-t-xl m-1">
                <label htmlFor="description" className="sr-only">
                  توضیحات گزارش
                </label>
                <textarea
                  id="description"
                  rows="4"
                  className="w-full px-0 text-sm text-gray-900 bg-white border-0  focus:ring-0 "
                  placeholder="در رابطه با مواردی که مشاهده کردید بنویسید"
                  required
                ></textarea>
              </div>
              <div className="flex items-center justify-between px-3 py-2">
                <div></div>
                <div className="flex ps-0 space-x-1 rtl:space-x-reverse sm:ps-2">
                  <button
                    type="button"
                    className="inline-flex justify-center items-center p-2 text-bla rounded-md cursor-pointer hover:bg-gray-400  bg-light-neutral-background-high"
                  >
                    <MicrophoneIcon size={20} />
                    <span className="sr-only">افزودن فایل</span>
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center items-center p-2 text-bla rounded-md cursor-pointer hover:bg-gray-400  bg-light-neutral-background-high"
                  >
                    <PaperclipIcon size={20} />
                    <span className="sr-only">ضبط صدا</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="w-full border"></div>

          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-light-neutral-text-high font-body-medium text-gray-900 "
            >
              موقعیت مکانی
            </label>
            <div className="w-full h-64 rounded-xl overflow-hidden border border-light-neutral-border-medium-rest relative z-10">
              <MapContainer
                center={[selectedLocation.lat, selectedLocation.lng]}
                zoom={13}
                style={{ height: "100%", width: "100%" }}
                onClick={handleMapClick}
              >
                <TileLayer
                  url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                />
                <Marker position={[selectedLocation.lat, selectedLocation.lng]}>
                  <Popup>
                    موقعیت انتخاب شده
                    <br />
                    عرض جغرافیایی: {selectedLocation.lat.toFixed(6)}
                    <br />
                    طول جغرافیایی: {selectedLocation.lng.toFixed(6)}
                  </Popup>
                </Marker>
              </MapContainer>
            </div>
            <span className="text-light-neutral-text-medium font-body-small m-1">
              برای انتخاب موقعیت، روی نقشه کلیک کنید
            </span>
          </div>
          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-light-neutral-text-high font-body-medium text-gray-900 "
            >
              آدرس
            </label>
            <input
              type="text"
              id="address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              className="bg-white border border-light-neutral-border-medium-rest text-light-neutral-text-high rounded-xl block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              placeholder="آدرس را وارد کنید"
              required
            />
            <span className="text-light-neutral-text-medium font-body-small m-1">
              پس از انتخاب بر روی نقشه به صورت خودکار آدرس تعیین می‌شود
            </span>
          </div>

          <div className="w-full border"></div>

          <div>
            <label
              htmlFor="title"
              className="block mb-2 text-light-neutral-text-high font-body-medium text-gray-900 "
            >
              شماره موبایل (اختیاری)
            </label>
            <input
              type="text"
              id="title"
              className="bg-white border border-light-neutral-border-medium-rest text-light-neutral-text-high rounded-xl focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
              placeholder="۰۹۱۲*******"
              required
            />
          </div>

          <CButton
            type="submit"
            size="lg"
            rightIcon={<CaretLeftIcon className="mr-2" />}
          >
            ارسال گزارش
          </CButton>
        </div>
      </div>

      {/* Subject Bottom Sheet */}
      <SubjectBottomSheet
        isOpen={isSubjectBottomSheetOpen}
        onClose={() => setIsSubjectBottomSheetOpen(false)}
        selectedSubject={selectedSubject}
        onSelectSubject={setSelectedSubject}
      />
    </div>
  );
}

export default FormPage;
