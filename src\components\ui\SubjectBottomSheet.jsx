import { CheckIcon } from "@phosphor-icons/react";
import BottomSheet from "./BottomSheet";

const SubjectBottomSheet = ({
  isOpen,
  onClose,
  selectedSubject,
  onSelectSubject,
}) => {
  const subjects = [
    { id: 1, title: "امنیتی", description: "" },
    { id: 2, title: "پلیسی", description: "" },
    { id: 3, title: "اجتماعی", description: "" },
    { id: 4, title: "فرهنگی", description: "" },
    { id: 5, title: "مذهبی", description: "" },
    { id: 6, title: "سایر", description: "" },
  ];

  const handleSelectSubject = (subject) => {
    onSelectSubject(subject);
    onClose();
  };

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={onClose}
      title="انتخاب موضوع"
      height=""
    >
      <div className="space-y-2">
        {subjects.map((subject) => (
          <div
            key={subject.id}
            onClick={() => handleSelectSubject(subject)}
            className={`p-4 rounded-xl border cursor-pointer transition-all duration-200 ${
              selectedSubject?.id === subject.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
            }`}
          >
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 mb-1">
                  {subject.title}
                </h4>
                <p className="text-sm text-gray-600">{subject.description}</p>
              </div>
              {selectedSubject?.id === subject.id && (
                <CheckIcon size={20} className="text-blue-500 mr-3" />
              )}
            </div>
          </div>
        ))}
      </div>
    </BottomSheet>
  );
};

export default SubjectBottomSheet;
