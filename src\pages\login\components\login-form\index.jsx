import { useContext, useState } from "react";
import { Formik, Form, Field } from "formik";
import { useSearchParams } from "react-router-dom";
import AuthService from "service/api/authService.js";
import { loginSchema } from "utils/validationSchemas.js";
import logoImg from "assets/images/logo_main.png";
import { CButton } from "components/ui/CButton.jsx";
import { CaretLeft } from "@phosphor-icons/react";
import { CInput } from "components/ui/CInput.jsx";
import { FormErrorWrapper } from "components/ui/FormErrorArea.jsx";

const LoginForm = ({ handleLogin }) => {
  const [searchParams] = useSearchParams();

  const [formErrorTitle, setFormErrorTitle] = useState(null);
  const [formErrorText, setFormErrorText] = useState(null);

  const handleSubmit = async (values, { setSubmitting, setFieldError }) => {
    setFormErrorTitle(null);
    try {
      const response = await AuthService.loginUser(values);
      if (!(response?.data?.code === 200)) {
        setFormErrorTitle("خطا در ورود");
        // setSnackBarData({
        //     show: true,
        //     data: {
        //         text: 'خطا در ورود.',
        //         type: 'error',
        //     },
        // });
        setSubmitting(false);
        return false;
      }

      const { access, refresh } = response?.data?.data;

      if (!access || !refresh) return false;

      setTokens(access, refresh);
      login(response?.data?.data);

      // setSnackBarData({
      //     show: true,
      //     data: {
      //         text: 'باموفقیت وارد شدید.',
      //         type: 'success',
      //     },
      // });

      setSubmitting(false);

      if (searchParams.get("redirect_url")) {
        window.location.href = searchParams.get("redirect_url");
      } else {
        window.location.href = "/app/dashboard/";
      }
    } catch (error) {
      console.log(error);
      if (error.response) {
        setFieldError("general", error.response.data.message);
        setFieldError("username", " ");
        setFieldError("password", " ");
        setFormErrorTitle(error.response.data.message);
        // setSnackBarData({
        //     show: true,
        //     data: {
        //         text: error.response.data.message,
        //         type: 'error',
        //     },
        // });
      } else if (error.request) {
        // The request was made but no response was received
        console.error("No response received:", error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error("Error", error.message);
      }
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Formik
      initialValues={{ username: "", password: "" }}
      validationSchema={loginSchema}
      onSubmit={handleSubmit}
    >
      {({ isSubmitting, handleChange, handleBlur }) => (
        <Form className="font-body-medium flex-1 bg-white mx-auto w-full h-full max-w-lg overflow-hidden">
          {/* Title */}
          <img
            className={"top-0 w-auto m-auto h-[96px]"}
            src={logoImg}
            alt="logo"
          />
          <h2 className="font-headline-medium text-gray-800 mt-12 mb-5 text-right">
            ورود به تحلیل
          </h2>
          <Field
            id={"username"}
            name={"username"}
            component={CInput}
            size={"lg"}
            validation={"none"}
            direction={"ltr"}
            title={"نام کاربری"}
            placeholder={"نام کاربری خود را وارد کنید"}
            onChange={handleChange("username")}
            onBlur={handleBlur("username")}
          ></Field>

          <Field
            id={"password"}
            name={"password"}
            type={"password"}
            component={CInput}
            size={"lg"}
            validation={"none"}
            direction={"ltr"}
            title={"رمز عبور"}
            placeholder={"رمز عبور خود را وارد کنید"}
            onChange={handleChange("password")}
            onBlur={handleBlur("password")}
          ></Field>

          {/* Forgot password link */}
          <div className="mb-4 text-left">
            <a href="/forgot-password" className="text-light-primary-text-rest">
              رمز عبور خود را فراموش کردم
            </a>
          </div>

          <FormErrorWrapper
            show={!!formErrorTitle}
            title={formErrorTitle}
            text={formErrorText}
          />

          {/* Submit button */}
          <div className="flex justify-end">
            <CButton
              rightIcon={<CaretLeft />}
              type={"submit"}
              size={"lg"}
              disabled={isSubmitting}
            >
              ورود
            </CButton>
          </div>
        </Form>
      )}
    </Formik>
  );
};

export default LoginForm;
