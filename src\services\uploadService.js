// Mock upload service - replace with your actual API endpoint
export const uploadFile = async (file, onProgress) => {
  return new Promise((resolve, reject) => {
    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.random() * 15;
      if (progress > 100) progress = 100;
      
      onProgress(Math.round(progress));
      
      if (progress >= 100) {
        clearInterval(interval);
        // Simulate successful upload response
        resolve({
          success: true,
          fileId: `file_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          url: URL.createObjectURL(file), // In real app, this would be the server URL
          fileName: file.name,
          fileSize: file.size,
          fileType: file.type,
          uploadedAt: new Date().toISOString()
        });
      }
    }, 200 + Math.random() * 300); // Random interval between 200-500ms

    // Simulate potential upload failure (uncomment to test error handling)
    // setTimeout(() => {
    //   if (Math.random() < 0.1) { // 10% chance of failure
    //     clearInterval(interval);
    //     reject(new Error('Upload failed: Network error'));
    //   }
    // }, 2000);
  });
};

// Real implementation example:
/*
export const uploadFile = async (file, onProgress) => {
  const formData = new FormData();
  formData.append('file', file);

  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener('progress', (event) => {
      if (event.lengthComputable) {
        const progress = Math.round((event.loaded / event.total) * 100);
        onProgress(progress);
      }
    });

    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText);
          resolve(response);
        } catch (error) {
          reject(new Error('Invalid response format'));
        }
      } else {
        reject(new Error(`Upload failed with status: ${xhr.status}`));
      }
    });

    xhr.addEventListener('error', () => {
      reject(new Error('Network error during upload'));
    });

    xhr.open('POST', 'YOUR_UPLOAD_API_ENDPOINT');
    xhr.send(formData);
  });
};
*/
