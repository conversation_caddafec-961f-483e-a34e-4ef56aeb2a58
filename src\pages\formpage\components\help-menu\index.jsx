import { Link } from "react-router-dom";

const HelpMenu = () => {
  return (
    <div className="inline-flex items-center justify-center gap-[16px] relative">
      <a
        href={"https://www.stinas.ir"}
        target="blank"
        className="relative w-fit mt-[-1.00px] font-body-medium font-[number:var(--body-medium-font-weight)] text-schema-neutral-text-medium text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] whitespace-nowrap [direction:rtl] [font-style:var(--body-medium-font-style)]"
      >
        راهنمای استفاده
      </a>
      <div className="relative w-[6px] h-[6px] bg-schema-neutral-text-low rounded-[3px] bg-neutral-400" />
      <a
        href={"https://www.stinas.ir"}
        target="blank"
        className="relative w-fit mt-[-1.00px] font-body-medium font-[number:var(--body-medium-font-weight)] text-schema-neutral-text-medium text-[length:var(--body-medium-font-size)] tracking-[var(--body-medium-letter-spacing)] leading-[var(--body-medium-line-height)] whitespace-nowrap [direction:rtl] [font-style:var(--body-medium-font-style)]"
      >
        درباره ما
      </a>
    </div>
  );
};

export default HelpMenu;
