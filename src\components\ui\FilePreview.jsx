import { useState, useEffect } from "react";
import { XIcon, PlayIcon, FileIcon, ImageIcon } from "@phosphor-icons/react";

const FilePreview = ({ file, onRemove, onPreview, uploadProgress = 0 }) => {
  const [preview, setPreview] = useState(null);
  const [fileType, setFileType] = useState('file');

  useEffect(() => {
    if (file.type.startsWith('image/')) {
      setFileType('image');
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target.result);
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('video/')) {
      setFileType('video');
      const reader = new FileReader();
      reader.onload = (e) => setPreview(e.target.result);
      reader.readAsDataURL(file);
    } else {
      setFileType('file');
    }
  }, [file]);

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = () => {
    if (fileType === 'image') return <ImageIcon size={24} className="text-blue-500" />;
    if (fileType === 'video') return <PlayIcon size={24} className="text-green-500" />;
    return <FileIcon size={24} className="text-gray-500" />;
  };

  const handlePreviewClick = () => {
    if (fileType === 'image' || fileType === 'video') {
      onPreview(file, preview);
    }
  };

  return (
    <div className="relative w-24 h-24 bg-gray-100 rounded-lg border border-gray-200 overflow-hidden group">
      {/* Progress Circle */}
      {uploadProgress < 100 && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20">
          <div className="relative w-12 h-12">
            <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
              <path
                className="text-gray-300"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
              <path
                className="text-blue-500"
                stroke="currentColor"
                strokeWidth="3"
                fill="none"
                strokeDasharray={`${uploadProgress}, 100`}
                d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="text-white text-xs font-medium">{Math.round(uploadProgress)}%</span>
            </div>
          </div>
        </div>
      )}

      {/* Remove Button */}
      <button
        onClick={onRemove}
        className="absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity z-30"
      >
        <XIcon size={12} />
      </button>

      {/* File Content */}
      <div 
        className={`w-full h-full flex items-center justify-center ${
          (fileType === 'image' || fileType === 'video') ? 'cursor-pointer' : ''
        }`}
        onClick={handlePreviewClick}
      >
        {fileType === 'image' && preview ? (
          <img 
            src={preview} 
            alt={file.name}
            className="w-full h-full object-cover"
          />
        ) : fileType === 'video' && preview ? (
          <div className="relative w-full h-full">
            <video 
              src={preview}
              className="w-full h-full object-cover"
              muted
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
              <PlayIcon size={20} className="text-white" />
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center p-2 text-center">
            {getFileIcon()}
            <span className="text-xs text-gray-600 mt-1 truncate w-full">
              {file.name.length > 8 ? file.name.substring(0, 8) + '...' : file.name}
            </span>
          </div>
        )}
      </div>

      {/* File Info Tooltip */}
      <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="truncate">{file.name}</div>
        <div>{formatFileSize(file.size)}</div>
      </div>
    </div>
  );
};

export default FilePreview;
