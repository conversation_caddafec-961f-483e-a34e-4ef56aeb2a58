import { useState, useRef, useEffect } from "react";
import { 
  MicrophoneIcon, 
  StopIcon, 
  TrashIcon, 
  PaperPlaneIcon,
  PlayIcon,
  PauseIcon 
} from "@phosphor-icons/react";

const VoiceRecorder = ({ onSendVoice, onCancel }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  
  const mediaRecorderRef = useRef(null);
  const audioRef = useRef(null);
  const timerRef = useRef(null);
  const chunksRef = useRef([]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        const blob = new Blob(chunksRef.current, { type: 'audio/wav' });
        setAudioBlob(blob);
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      setRecordingTime(0);
      
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('Error accessing microphone:', error);
      alert('خطا در دسترسی به میکروفون. لطفاً مجوز دسترسی را بررسی کنید.');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
    setIsPaused(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const pauseRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'recording') {
      mediaRecorderRef.current.pause();
      setIsPaused(true);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  };

  const resumeRecording = () => {
    if (mediaRecorderRef.current && mediaRecorderRef.current.state === 'paused') {
      mediaRecorderRef.current.resume();
      setIsPaused(false);
      timerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    }
  };

  const playAudio = () => {
    if (audioBlob && audioRef.current) {
      const audioUrl = URL.createObjectURL(audioBlob);
      audioRef.current.src = audioUrl;
      audioRef.current.play();
      setIsPlaying(true);
      
      audioRef.current.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
      };
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const deleteRecording = () => {
    setAudioBlob(null);
    setRecordingTime(0);
    setIsPlaying(false);
    if (audioRef.current) {
      audioRef.current.src = '';
    }
    onCancel();
  };

  const sendVoice = () => {
    if (audioBlob) {
      onSendVoice(audioBlob, recordingTime);
      deleteRecording();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="border border-gray-200 rounded-xl p-4 bg-gray-50 mt-2">
      <audio ref={audioRef} style={{ display: 'none' }} />
      
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {/* Recording Controls */}
          {!audioBlob && (
            <>
              {!isRecording ? (
                <button
                  onClick={startRecording}
                  className="flex items-center justify-center w-10 h-10 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                >
                  <MicrophoneIcon size={20} />
                </button>
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <button
                    onClick={stopRecording}
                    className="flex items-center justify-center w-10 h-10 bg-gray-500 hover:bg-gray-600 text-white rounded-full transition-colors"
                  >
                    <StopIcon size={20} />
                  </button>
                  {!isPaused ? (
                    <button
                      onClick={pauseRecording}
                      className="flex items-center justify-center w-8 h-8 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full transition-colors"
                    >
                      <PauseIcon size={16} />
                    </button>
                  ) : (
                    <button
                      onClick={resumeRecording}
                      className="flex items-center justify-center w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
                    >
                      <PlayIcon size={16} />
                    </button>
                  )}
                </div>
              )}
            </>
          )}

          {/* Playback Controls */}
          {audioBlob && (
            <button
              onClick={isPlaying ? pauseAudio : playAudio}
              className="flex items-center justify-center w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
            >
              {isPlaying ? <PauseIcon size={20} /> : <PlayIcon size={20} />}
            </button>
          )}

          {/* Timer/Status */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {isRecording && (
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-red-500 font-medium">در حال ضبط</span>
              </div>
            )}
            {isPaused && (
              <span className="text-yellow-500 font-medium">متوقف شده</span>
            )}
            {audioBlob && !isRecording && (
              <span className="text-green-500 font-medium">ضبط شده</span>
            )}
            <span className="text-gray-600 font-mono">
              {formatTime(recordingTime)}
            </span>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <button
            onClick={deleteRecording}
            className="flex items-center justify-center w-8 h-8 bg-red-100 hover:bg-red-200 text-red-600 rounded-full transition-colors"
          >
            <TrashIcon size={16} />
          </button>
          
          {audioBlob && (
            <button
              onClick={sendVoice}
              className="flex items-center justify-center w-8 h-8 bg-blue-100 hover:bg-blue-200 text-blue-600 rounded-full transition-colors"
            >
              <PaperPlaneIcon size={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceRecorder;
