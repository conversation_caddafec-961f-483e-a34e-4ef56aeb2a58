import { useState, useRef, useEffect } from "react";
import {
  MicrophoneIcon,
  StopIcon,
  TrashIcon,
  PaperPlaneIcon,
  PlayIcon,
  PauseIcon,
  PaperPlaneRightIcon,
} from "@phosphor-icons/react";

const VoiceRecorder = ({ onSendVoice, onCancel }) => {
  const [isRecording, setIsRecording] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);

  const mediaRecorderRef = useRef(null);
  const audioRef = useRef(null);
  const timerRef = useRef(null);
  const chunksRef = useRef([]);

  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
      if (
        mediaRecorderRef.current &&
        mediaRecorderRef.current.state !== "inactive"
      ) {
        mediaRecorderRef.current.stop();
      }
    };
  }, []);

  const checkBrowserSupport = () => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      alert(
        "مرورگر شما از ضبط صدا پشتیبانی نمی‌کند. لطفاً از مرورگر جدیدتری استفاده کنید."
      );
      return false;
    }

    if (!window.MediaRecorder) {
      alert("مرورگر شما از MediaRecorder پشتیبانی نمی‌کند.");
      return false;
    }

    // Check if running on HTTPS or localhost
    if (
      location.protocol !== "https:" &&
      location.hostname !== "localhost" &&
      location.hostname !== "127.0.0.1"
    ) {
      alert("ضبط صدا فقط در محیط امن (HTTPS) یا localhost امکان‌پذیر است.");
      return false;
    }

    return true;
  };

  const startRecording = async () => {
    if (!checkBrowserSupport()) {
      return;
    }

    try {
      console.log("Requesting microphone access...");

      const constraints = {
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100,
        },
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log("Microphone access granted");

      // Check MediaRecorder support with different MIME types
      let mimeType = "audio/webm";
      if (MediaRecorder.isTypeSupported("audio/webm;codecs=opus")) {
        mimeType = "audio/webm;codecs=opus";
      } else if (MediaRecorder.isTypeSupported("audio/webm")) {
        mimeType = "audio/webm";
      } else if (MediaRecorder.isTypeSupported("audio/mp4")) {
        mimeType = "audio/mp4";
      } else if (MediaRecorder.isTypeSupported("audio/ogg")) {
        mimeType = "audio/ogg";
      }

      mediaRecorderRef.current = new MediaRecorder(stream, { mimeType });
      chunksRef.current = [];

      mediaRecorderRef.current.ondataavailable = (event) => {
        console.log("Data available:", event.data.size);
        if (event.data.size > 0) {
          chunksRef.current.push(event.data);
        }
      };

      mediaRecorderRef.current.onstop = () => {
        console.log("Recording stopped, creating blob...");
        const blob = new Blob(chunksRef.current, { type: mimeType });
        console.log("Blob created:", blob.size, "bytes");
        setAudioBlob(blob);
        stream.getTracks().forEach((track) => track.stop());
      };

      mediaRecorderRef.current.onerror = (event) => {
        console.error("MediaRecorder error:", event.error);
        alert("خطا در ضبط صدا: " + event.error.message);
      };

      mediaRecorderRef.current.start(1000); // Collect data every second
      setIsRecording(true);
      setRecordingTime(0);

      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);

      console.log("Recording started");
    } catch (error) {
      console.error("Error accessing microphone:", error);
      let errorMessage = "خطا در دسترسی به میکروفون: ";

      if (error.name === "NotAllowedError") {
        errorMessage +=
          "دسترسی به میکروفون رد شد. لطفاً در تنظیمات مرورگر اجازه دهید.";
      } else if (error.name === "NotFoundError") {
        errorMessage += "میکروفون یافت نشد.";
      } else if (error.name === "NotReadableError") {
        errorMessage += "میکروفون در حال استفاده توسط برنامه دیگری است.";
      } else {
        errorMessage += error.message;
      }

      alert(errorMessage);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state !== "inactive"
    ) {
      mediaRecorderRef.current.stop();
    }
    setIsRecording(false);
    setIsPaused(false);
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
  };

  const pauseRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.pause();
      setIsPaused(true);
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    }
  };

  const resumeRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "paused"
    ) {
      mediaRecorderRef.current.resume();
      setIsPaused(false);
      timerRef.current = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    }
  };

  const playAudio = () => {
    if (audioBlob && audioRef.current) {
      const audioUrl = URL.createObjectURL(audioBlob);
      audioRef.current.src = audioUrl;
      audioRef.current.play();
      setIsPlaying(true);

      audioRef.current.onended = () => {
        setIsPlaying(false);
        URL.revokeObjectURL(audioUrl);
      };
    }
  };

  const pauseAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const deleteRecording = () => {
    setAudioBlob(null);
    setRecordingTime(0);
    setIsPlaying(false);
    if (audioRef.current) {
      audioRef.current.src = "";
    }
    onCancel();
  };

  const sendVoice = () => {
    if (audioBlob) {
      onSendVoice(audioBlob, recordingTime);
      deleteRecording();
    }
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  // Check browser support on component mount
  const [browserSupported, setBrowserSupported] = useState(true);

  useEffect(() => {
    const supported = !!(
      navigator.mediaDevices &&
      navigator.mediaDevices.getUserMedia &&
      window.MediaRecorder
    );
    setBrowserSupported(supported);

    // Start recording automatically when component mounts
    // if (supported) {
    //   startRecording();
    // }
  }, []);

  if (!browserSupported) {
    return (
      <div className="border border-red-200 rounded-xl p-4 bg-red-50 mt-2">
        <div className="text-red-600 text-center">
          <p className="font-medium">مرورگر شما از ضبط صدا پشتیبانی نمی‌کند</p>
          <p className="text-sm mt-1">
            لطفاً از Chrome، Firefox، Safari یا Edge جدید استفاده کنید
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className=" rounded-xl p-4 bg-light-primary-background-highlight mt-2">
      <audio ref={audioRef} style={{ display: "none" }} />

      <div className="flex items-center justify-between">
        {/* Action Buttons */}
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          {audioBlob && (
            <button
              onClick={sendVoice}
              className="flex items-center justify-center w-10 h-10 bg-light-primary-background-rest hover:bg-light-primary-background-hover text-black rounded-lg transition-colors"
            >
              <PaperPlaneRightIcon size={22} />
            </button>
          )}
          <button
            onClick={deleteRecording}
            className="flex items-center justify-center w-10 h-10 bg-red-100 hover:bg-red-200 text-red-600 rounded-lg transition-colors"
          >
            <TrashIcon size={22} />
          </button>
        </div>

        <div className="flex items-center space-x-3 rtl:space-x-reverse">
          {/* Timer/Status */}
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {isRecording && (
              <div className="flex items-center space-x-1 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-red-500 font-medium">در حال ضبط</span>
              </div>
            )}
            {isPaused && (
              <span className="text-yellow-500 font-medium">متوقف شده</span>
            )}
            {audioBlob && !isRecording && (
              <span className="text-green-500 font-medium">ضبط شده</span>
            )}
            <span className="text-gray-600 font-mono">
              {formatTime(recordingTime)}
            </span>
          </div>
          {/* Recording Controls */}
          {!audioBlob && (
            <>
              {!isRecording ? (
                <button
                  onClick={startRecording}
                  className="flex items-center justify-center w-10 h-10 bg-red-500 hover:bg-red-600 text-white rounded-full transition-colors"
                >
                  <MicrophoneIcon size={20} />
                </button>
              ) : (
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <button
                    onClick={stopRecording}
                    className="flex items-center justify-center w-10 h-10 bg-gray-500 hover:bg-gray-600 text-white rounded-full transition-colors"
                  >
                    <StopIcon size={20} />
                  </button>
                  {/* {!isPaused ? (
                    <button
                      onClick={pauseRecording}
                      className="flex items-center justify-center w-8 h-8 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full transition-colors"
                    >
                      <PauseIcon size={16} />
                    </button>
                  ) : (
                    <button
                      onClick={resumeRecording}
                      className="flex items-center justify-center w-8 h-8 bg-green-500 hover:bg-green-600 text-white rounded-full transition-colors"
                    >
                      <PlayIcon size={16} />
                    </button>
                  )} */}
                </div>
              )}
            </>
          )}

          {/* Playback Controls */}
          {audioBlob && (
            <button
              onClick={isPlaying ? pauseAudio : playAudio}
              className="flex items-center justify-center w-10 h-10 bg-blue-500 hover:bg-blue-600 text-white rounded-full transition-colors"
            >
              {isPlaying ? <PauseIcon size={20} /> : <PlayIcon size={20} />}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default VoiceRecorder;
