{"name": "hodhod-pwa", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "local": "vite --port 3030", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "host": "vite --host", "docker-dev-up": "docker-compose -f docker-compose.dev.yml up", "docker-dev-down": "docker-compose -f docker-compose.dev.yml down"}, "dependencies": {"@headlessui/react": "^2.2.3", "@phosphor-icons/react": "^2.0.15", "axios": "1.7.4", "formik": "^2.4.5", "leaflet": "^1.9.4", "prop-types": "^15.8.1", "query-string": "^9.0.0", "react": "^18.3.1", "react-date-object": "^2.1.9", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-multi-date-picker": "^4.5.2", "react-otp-input": "^3.1.1", "react-router-dom": "^6.30.1", "react-toastify": "^10.0.4", "react-use": "^17.5.0", "tailwind-merge": "^2.3.0", "yup": "^1.3.3"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "5.4.6"}}